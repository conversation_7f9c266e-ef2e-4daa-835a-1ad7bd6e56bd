"use client";
import React, {
  useContext,
  useMemo,
  useCallback,
  memo,
  useRef,
  useState,
  useEffect,
} from "react";
import { Box, Typography, styled } from "@mui/material";
import { StoreContext } from "../../store";
import { observer } from "mobx-react";
import {
  formatTime,
  calculateTimelinePosition,
  getTimelineContainerWidth,
  TIMELINE_CONSTANTS,
} from "../../utils/timeUtils";

// 样式组件
const MarkerContainer = styled(Box)(({ theme }) => ({
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  height: "42px",
  paddingLeft: "0px", // 移除左侧padding，使用计算的位置来对齐
  pointerEvents: "auto",
  zIndex: 30,
  backgroundColor: "transparent", // 将背景设置为透明
  cursor: "pointer",
}));

const Marker = styled(Box)(({ theme }) => ({
  position: "absolute",
  width: "1px",
  height: "6px",
  backgroundColor:
    theme.palette.mode === "dark"
      ? theme.palette.grey[600]
      : theme.palette.grey[400],
  top: "12px",
  transition: "height 0.2s ease",
  "&:hover": {
    height: "8px",
  },
}));

const MajorMarker = styled(Marker)(({ theme }) => ({
  height: "12px",
  width: "1px",
  color: theme.palette.text.secondary,
  top: "8px",
  borderRadius: "1px",
}));

const MarkerLabel = styled(Typography)(({ theme }) => ({
  position: "absolute",
  fontSize: "0.7rem",
  fontWeight: 500,
  color: theme.palette.text.secondary,
  transform: "translateX(-50%)",
  whiteSpace: "nowrap",
  userSelect: "none",
  opacity: 0.9,
  transition: "opacity 0.2s ease, transform 0.2s ease",
}));

// 将 possibleIntervals 移到函数外部，避免重复创建
const possibleIntervals = [
  100, // 0.1秒
  1000, // 1秒
  2000, // 2秒
  5000, // 5秒
  10000, // 10秒
  15000, // 15秒
  30000, // 30秒
  60000, // 1分钟
  120000, // 2分钟
  180000, // 3分钟
  300000, // 5分钟
  600000, // 10分钟
  900000, // 15分钟
  1800000, // 30分钟
  3600000, // 1小时
  7200000, // 2小时
  10800000, // 3小时
  18000000, // 5小时
  86400000, // 1天
];

// 根据时间线显示持续时间计算适当的时间标记间隔
// 将函数移出组件外部，避免每次渲染都重新创建
const calculateIntervals = (timelineDisplayDuration: number) => {
  // 最佳标记密度的目标数量
  const minMajorMarkers = 5;
  const maxMajorMarkers = 15;
  const optimalMajorMarkers = 8;

  // 计算最佳密度的理想间隔
  const idealInterval = timelineDisplayDuration / optimalMajorMarkers;

  // 寻找最接近理想密度的间隔
  let bestInterval = possibleIntervals[0];
  let bestScoreForDensity = Number.MAX_SAFE_INTEGER;

  for (const interval of possibleIntervals) {
    const markerCount = Math.ceil(timelineDisplayDuration / interval);

    // 检查标记数量是否在可接受范围内
    if (markerCount >= minMajorMarkers && markerCount <= maxMajorMarkers) {
      // 计算此间隔与理想密度的接近程度
      const densityScore = Math.abs(interval - idealInterval);

      // 如果更好，则更新
      if (densityScore < bestScoreForDensity) {
        bestScoreForDensity = densityScore;
        bestInterval = interval;
      }
    }
  }

  // 如果没有找到合适的间隔，选择最接近理想间隔的值
  if (bestScoreForDensity === Number.MAX_SAFE_INTEGER) {
    bestInterval = possibleIntervals.reduce((prev, curr) =>
      Math.abs(curr - idealInterval) < Math.abs(prev - idealInterval)
        ? curr
        : prev
    );
  }

  // 根据主要间隔动态计算次要间隔
  let minorMarkersPerMajor = 4; // 默认每个主要标记之间有4个次要标记

  if (bestInterval <= 1000) {
    minorMarkersPerMajor = 1; // 对于非常小的间隔，使用更少的次要标记
  } else if (bestInterval <= 5000) {
    minorMarkersPerMajor = 2; // 对于小间隔
  } else if (bestInterval <= 60000) {
    minorMarkersPerMajor = 4; // 对于中等间隔
  } else {
    minorMarkersPerMajor = 5; // 对于大间隔
  }

  return {
    majorInterval: bestInterval,
    minorInterval: bestInterval / (minorMarkersPerMajor + 1),
    minorMarkersCount: minorMarkersPerMajor,
  };
};

// 使用memo优化单个标记的渲染
const TimeMarker = memo(
  ({
    position,
    isMajor,
    time,
  }: {
    position: string;
    isMajor: boolean;
    time: number;
  }) => (
    <>
      {isMajor ? (
        <>
          <MajorMarker
            style={{ left: position }}
            className="time-scale-marker"
          />
          <MarkerLabel
            style={{ left: position, top: "22px" }}
            className="time-scale-marker-label"
          >
            {formatTime(time)}
          </MarkerLabel>
        </>
      ) : (
        <Marker style={{ left: position }} className="time-scale-marker" />
      )}
    </>
  )
);

TimeMarker.displayName = "TimeMarker";

// 导出组件
export const TimeScaleMarkers = observer(() => {
  const store = useContext(StoreContext);
  const { HANDLE_WIDTH } = TIMELINE_CONSTANTS;

  // 使用useRef和useEffect来获取和更新容器宽度
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = useState<number | null>(null);

  // 使用ResizeObserver监听容器宽度变化
  useEffect(() => {
    // 初始化时获取一次容器宽度
    setContainerWidth(getTimelineContainerWidth());

    // 创建ResizeObserver监听容器宽度变化
    const observer = new ResizeObserver(() => {
      setContainerWidth(getTimelineContainerWidth());
    });

    // 获取容器元素并开始监听
    const container = document.querySelector(".timeline-container");
    if (container) {
      observer.observe(container);
    }

    // 清理函数
    return () => {
      if (container) {
        observer.unobserve(container);
      }
      observer.disconnect();
    };
  }, []);

  // 根据时间线显示持续时间计算间隔
  const { majorInterval, minorMarkersCount } = useMemo(
    () => calculateIntervals(store.timelineDisplayDuration),
    [store.timelineDisplayDuration]
  );

  // 处理标记点击
  const handleMarkerClick = useCallback(
    (event: React.MouseEvent) => {
      const container = event.currentTarget as HTMLElement;
      const rect = container.getBoundingClientRect();
      const clickX = event.clientX - rect.left;

      // 计算有效内容区域宽度
      const contentWidth = container.clientWidth - HANDLE_WIDTH;

      // 计算点击位置在时间轴上的相对位置（考虑左侧排序把手宽度）
      const clickPosition = Math.max(0, clickX - HANDLE_WIDTH) / contentWidth;

      // 计算对应的时间点
      let clickTimeMs =
        store.timelinePan.offsetX +
        clickPosition * store.timelineDisplayDuration;

      // 如果有元素，限制不超过所有元素的最大endtime
      if (store.maxDuration > 0) {
        clickTimeMs = Math.min(clickTimeMs, store.maxDuration);
      }

      // 移动指示器
      store.handleSeek(clickTimeMs);
    },
    [
      store.timelinePan.offsetX,
      store.timelineDisplayDuration,
      store.maxDuration,
      store.handleSeek,
      HANDLE_WIDTH,
    ]
  );

  // 生成标记 - 优化依赖项列表
  const markers = useMemo(() => {
    // 如果容器宽度未知，返回空数组
    if (!containerWidth) return [];

    const result = [];

    // 计算可见时间范围
    const visibleStartTime = Math.max(0, store.timelinePan.offsetX);
    const visibleEndTime = visibleStartTime + store.timelineDisplayDuration;

    // 从可见范围之前的第一个主要标记开始
    const firstMajorMarkerTime =
      Math.floor(visibleStartTime / majorInterval) * majorInterval;

    // 预先计算一些重复使用的值
    const markerPadding = HANDLE_WIDTH;

    // 生成主要标记和标签
    for (
      let time = firstMajorMarkerTime;
      time <= visibleEndTime;
      time += majorInterval
    ) {
      if (time < 0) continue;

      // 计算位置 - 确保使用最新的容器宽度
      const position = calculateTimelinePosition(
        time,
        store.timelineDisplayDuration,
        store.timelinePan.offsetX,
        containerWidth
      );

      // 只添加在可见范围内的标记（带一些边距）
      if (position >= -5 && position <= 105) {
        result.push({
          time,
          position: `calc(${position}% + ${markerPadding}px)`,
          isMajor: true,
        });
      }
    }

    // 批量计算次要标记以减少循环嵌套
    const minorMarkers = [];

    // 在每个主要间隔内生成次要标记
    for (
      let majorTime = firstMajorMarkerTime;
      majorTime <= visibleEndTime;
      majorTime += majorInterval
    ) {
      if (majorTime < 0) continue;

      // 在主要标记之间生成均匀分布的次要标记
      for (let i = 1; i <= minorMarkersCount; i++) {
        const time = majorTime + (i * majorInterval) / (minorMarkersCount + 1);

        // 跳过超出时间线或为负的时间
        if (time > visibleEndTime || time < 0) continue;

        // 计算位置 - 确保使用最新的容器宽度
        const position = calculateTimelinePosition(
          time,
          store.timelineDisplayDuration,
          store.timelinePan.offsetX,
          containerWidth
        );

        // 只添加在可见范围内的标记
        if (position >= -5 && position <= 105) {
          minorMarkers.push({
            time,
            position: `calc(${position}% + ${markerPadding}px)`,
            isMajor: false,
          });
        }
      }
    }

    // 合并主要和次要标记
    return [...result, ...minorMarkers];
  }, [
    store.timelineDisplayDuration,
    store.timelinePan.offsetX,
    majorInterval,
    minorMarkersCount,
    HANDLE_WIDTH,
    containerWidth, // 添加容器宽度作为依赖项，确保宽度变化时重新计算
  ]);

  return (
    <MarkerContainer onClick={handleMarkerClick}>
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: -1,
        }}
      />
      {markers.map((marker) => (
        <TimeMarker
          key={marker.time}
          position={marker.position}
          isMajor={marker.isMajor}
          time={marker.time}
        />
      ))}
    </MarkerContainer>
  );
});
