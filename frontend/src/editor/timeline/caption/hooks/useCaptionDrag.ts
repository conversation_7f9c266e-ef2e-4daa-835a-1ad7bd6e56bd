import { useCallback, useState } from "react";
import { Caption } from "../../../../types";
import { formatTime, timeStringToMs } from "../../../../utils/timeUtils";
import {
  checkTimeOverlap,
  findFollowingCaptions,
  generateSnapPointsFromCaptions,
  findNearestSnapPoint,
} from "../../utils";

interface UseCaptionDragProps {
  caption: Caption;
  allCaptions: Caption[];
  containerWidth: number | null;
  store: any;
}

interface DragHandlers {
  handleLeftHandleDrag: (event: React.MouseEvent<HTMLDivElement>) => void;
  handleRightHandleDrag: (event: React.MouseEvent<HTMLDivElement>) => void;
  handleCenterDrag: (event: React.MouseEvent<HTMLDivElement>) => void;
  isDragging: boolean;
  isSnappedStart: boolean;
  isSnappedEnd: boolean;
}

export const useCaptionDrag = ({
  caption,
  allCaptions,
  containerWidth,
  store,
}: UseCaptionDragProps): DragHandlers => {
  const [isDragging, setIsDragging] = useState(false);
  const [isSnappedStart, setIsSnappedStart] = useState(false);
  const [isSnappedEnd, setIsSnappedEnd] = useState(false);

  const startTimeMs = timeStringToMs(caption.startTime);
  const endTimeMs = timeStringToMs(caption.endTime);

  // 统一的碰撞视觉反馈处理函数
  const handleCollisionFeedback = (
    itemEl: HTMLElement | null,
    isColliding: boolean,
    dragType: "left" | "right" | "center"
  ) => {
    if (!itemEl) return;

    if (isColliding) {
      itemEl.classList.add("panning-mode");
    } else {
      itemEl.classList.remove("panning-mode");
    }
  };

  // 左侧控制柄拖拽处理
  const handleLeftHandleDrag = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (isDragging || !containerWidth) return;

      const trackElement =
        document.querySelector(".timeline-container") ||
        event.currentTarget.parentElement;
      if (!trackElement) return;

      const leftHandleEl = document.getElementById("left-handle");
      const itemEl = document.getElementById(`caption-item-${caption.id}`);
      const snapPoints = generateSnapPointsFromCaptions(
        allCaptions,
        caption.id
      );

      // 记录初始位置，用于计算拖拽距离
      const initialPosition = {
        x: event.clientX,
        y: event.clientY,
      };

      // 添加拖拽预览效果的状态
      let dragPreviewActive = false;
      let dragDistance = 0;

      const initialStartTime = startTimeMs;
      const precedingCaptions = allCaptions
        .filter((c) => c.id !== caption.id)
        .filter((c) => timeStringToMs(c.endTime) <= initialStartTime)
        .map((c) => ({
          id: c.id,
          startTime: timeStringToMs(c.startTime),
          endTime: timeStringToMs(c.endTime),
          offsetFromCurrent: initialStartTime - timeStringToMs(c.endTime),
        }))
        .sort((a, b) => b.endTime - a.endTime);

      const prevCaption =
        precedingCaptions.length > 0
          ? precedingCaptions.reduce((nearest, current) => {
              return nearest.offsetFromCurrent < current.offsetFromCurrent
                ? nearest
                : current;
            })
          : null;

      let isPanningMode = false;

      const handleMouseMove = (moveEvent: MouseEvent) => {
        // 计算拖拽距离
        dragDistance = Math.sqrt(
          Math.pow(moveEvent.clientX - initialPosition.x, 2) +
            Math.pow(moveEvent.clientY - initialPosition.y, 2)
        );

        // 只有当拖拽距离超过阈值时才开始拖拽
        if (!dragPreviewActive && dragDistance > 5) {
          dragPreviewActive = true;
          // 添加拖拽预览效果的类
          if (leftHandleEl) {
            leftHandleEl.classList.add("handle-dragging");
          }
          if (itemEl) {
            itemEl.classList.add("resize-active");
          }
        }

        const trackRect = trackElement.getBoundingClientRect();
        const trackX = moveEvent.clientX - trackRect.left;
        const trackPercent = Math.max(0, Math.min(1, trackX / trackRect.width));

        const visibleStartTime = store.timelinePan.offsetX;
        const newStartTime =
          visibleStartTime + trackPercent * store.timelineDisplayDuration;

        let safeStartTime = Math.min(newStartTime, endTimeMs - 100);

        // 生成吸附点并检查是否应该吸附
        const snapResult = findNearestSnapPoint(safeStartTime, snapPoints);

        if (snapResult.snapped) {
          safeStartTime = snapResult.time;
          // 确保吸附后仍然满足startTime < endTime的约束
          safeStartTime = Math.min(safeStartTime, endTimeMs - 100);
          setIsSnappedStart(true);

          // 添加吸附反馈
          if (leftHandleEl) {
            leftHandleEl.classList.add("snapped");
          }
        } else {
          setIsSnappedStart(false);

          // 移除吸附反馈
          if (leftHandleEl) {
            leftHandleEl.classList.remove("snapped");
          }
        }

        // 检测拖动方向，只有向左拖动时才考虑进入平移模式
        const isDraggingLeft = safeStartTime < initialStartTime;
        const isColliding =
          prevCaption &&
          isDraggingLeft &&
          (safeStartTime <= prevCaption.endTime ||
            Math.abs(safeStartTime - prevCaption.endTime) < 1);

        // 只在向左拖动并且碰撞时进入平移模式
        isPanningMode = isColliding;

        // 更新平移模式的视觉反馈
        handleCollisionFeedback(itemEl, isPanningMode, "left");

        if (!isPanningMode) {
          // 不在平移模式，只更新当前字幕的startTime
          const formattedStart = formatTime(safeStartTime);
          const startTimeFormatted = store.formatCaptionTime(formattedStart);
          store.updateCaption(caption.id, "startTime", startTimeFormatted);
        } else {
          // 在平移模式（向左拖动碰撞），移动当前字幕和前面的字幕
          const timeDelta = safeStartTime - initialStartTime;

          const leftmostCaption =
            precedingCaptions[precedingCaptions.length - 1];
          if (leftmostCaption) {
            const newLeftmostStart = leftmostCaption.startTime + timeDelta;
            if (newLeftmostStart < 0) {
              return;
            }
          }

          const formattedStart = formatTime(safeStartTime);
          const startTimeFormatted = store.formatCaptionTime(formattedStart);
          store.updateCaption(caption.id, "startTime", startTimeFormatted);

          precedingCaptions.forEach(
            ({ id, startTime, endTime, offsetFromCurrent }, index) => {
              let newEnd: number;
              if (index === 0) {
                newEnd = safeStartTime;
              } else {
                newEnd = safeStartTime - offsetFromCurrent;
              }
              const duration = endTime - startTime;
              let newStart = newEnd - duration;

              // 确保平移后的字幕时间仍然合理
              if (newStart < 0) {
                newStart = 0;
                newEnd = duration;
              }

              // 最终验证：确保startTime < endTime
              if (newStart >= newEnd) {
                console.warn(`平移字幕 ${id} 时检测到时间错乱，跳过更新`);
                return;
              }

              const startFormatted = store.formatCaptionTime(
                formatTime(newStart)
              );
              const endFormatted = store.formatCaptionTime(formatTime(newEnd));
              store.updateCaptionTimeFrame(id, startFormatted, endFormatted);
            }
          );
        }
      };

      const handleMouseUp = () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        setIsDragging(false);
        setIsSnappedStart(false);
        isPanningMode = false;

        // 移除所有拖拽预览效果的类
        if (leftHandleEl) {
          leftHandleEl.classList.remove("handle-dragging");
          leftHandleEl.classList.remove("snapped");
        }

        if (itemEl) {
          // 移除所有可能的类
          itemEl.classList.remove("resize-active");
          itemEl.classList.remove("panning-mode");
          itemEl.classList.remove("dragging-active");
        }

        // 拖拽结束后才进行排序和防止重叠处理
        store.captionManager.sortCaptionsByStartTime();

        // 使用新的智能碰撞检测和修复函数
        // 让CaptionManager处理碰撞检测和修复，确保数据同步
        store.captionManager.preventAllCaptionsOverlap();
        // 同步回store.captions
        store.captions = store.captionManager.captions;

        // 保存更改
        store.saveChange();
      };

      setIsDragging(true);
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      if (store.playing) {
        store.setPlaying(false);
      }
    },
    [
      caption.id,
      startTimeMs,
      endTimeMs,
      containerWidth,
      isDragging,
      store,
      allCaptions,
    ]
  );

  // 右侧控制柄拖拽处理
  const handleRightHandleDrag = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (isDragging || !containerWidth) return;

      const trackElement =
        document.querySelector(".timeline-container") ||
        event.currentTarget.parentElement;
      if (!trackElement) return;

      const rightHandleEl = document.getElementById("right-handle");
      const itemEl = document.getElementById(`caption-item-${caption.id}`);
      const snapPoints = generateSnapPointsFromCaptions(
        allCaptions,
        caption.id
      );

      // 记录初始位置，用于计算拖拽距离
      const initialPosition = {
        x: event.clientX,
        y: event.clientY,
      };

      // 添加拖拽预览效果的状态
      let dragPreviewActive = false;
      let dragDistance = 0;

      const initialEndTime = endTimeMs;
      const followingCaptions = findFollowingCaptions(
        caption.id,
        initialEndTime,
        allCaptions
      );

      const nextCaption =
        followingCaptions.length > 0
          ? followingCaptions.reduce((nearest, current) => {
              return nearest.offsetFromCurrent < current.offsetFromCurrent
                ? nearest
                : current;
            })
          : null;

      let isPanningMode = false;

      const handleMouseMove = (moveEvent: MouseEvent) => {
        // 计算拖拽距离
        dragDistance = Math.sqrt(
          Math.pow(moveEvent.clientX - initialPosition.x, 2) +
            Math.pow(moveEvent.clientY - initialPosition.y, 2)
        );

        // 只有当拖拽距离超过阈值时才开始拖拽
        if (!dragPreviewActive && dragDistance > 5) {
          dragPreviewActive = true;
          // 添加拖拽预览效果的类
          if (rightHandleEl) {
            rightHandleEl.classList.add("handle-dragging");
          }
          if (itemEl) {
            itemEl.classList.add("resize-active");
          }
        }

        const trackRect = trackElement.getBoundingClientRect();
        const trackX = moveEvent.clientX - trackRect.left;
        const trackPercent = Math.max(0, Math.min(1, trackX / trackRect.width));

        const visibleStartTime = store.timelinePan.offsetX;
        const newEndTime =
          visibleStartTime + trackPercent * store.timelineDisplayDuration;

        let safeEndTime = Math.max(newEndTime, startTimeMs + 100);

        // 生成吸附点并检查是否应该吸附
        const snapResult = findNearestSnapPoint(safeEndTime, snapPoints);

        if (snapResult.snapped) {
          safeEndTime = snapResult.time;
          // 确保吸附后仍然满足endTime > startTime的约束
          safeEndTime = Math.max(safeEndTime, startTimeMs + 100);
          setIsSnappedEnd(true);

          // 添加吸附反馈
          if (rightHandleEl) {
            rightHandleEl.classList.add("snapped");
          }
        } else {
          setIsSnappedEnd(false);

          // 移除吸附反馈
          if (rightHandleEl) {
            rightHandleEl.classList.remove("snapped");
          }
        }

        // 检测拖动方向，只有向右拖动时才考虑进入平移模式
        const isDraggingRight = safeEndTime > initialEndTime;
        const isColliding =
          nextCaption &&
          isDraggingRight &&
          (safeEndTime >= nextCaption.startTime ||
            Math.abs(safeEndTime - nextCaption.startTime) < 1);

        // 只在向右拖动并且碰撞时进入平移模式
        isPanningMode = isColliding;

        // 更新平移模式的视觉反馈
        handleCollisionFeedback(itemEl, isPanningMode, "right");

        if (!isPanningMode) {
          // 不在平移模式，只更新当前字幕的endTime
          const formattedEnd = formatTime(safeEndTime);
          const endTimeFormatted = store.formatCaptionTime(formattedEnd);
          store.updateCaption(caption.id, "endTime", endTimeFormatted);
        } else {
          // 在平移模式（向右拖动碰撞），移动当前字幕和后面的字幕
          const formattedEnd = formatTime(safeEndTime);
          const endTimeFormatted = store.formatCaptionTime(formattedEnd);
          store.updateCaption(caption.id, "endTime", endTimeFormatted);

          followingCaptions.forEach(
            ({ id, startTime, endTime, offsetFromCurrent }, index) => {
              let newStart: number;
              if (index === 0) {
                newStart = safeEndTime;
              } else {
                newStart = safeEndTime + offsetFromCurrent;
              }
              const duration = endTime - startTime;
              let newEnd = newStart + duration;

              // 确保平移后的字幕时间仍然合理
              if (newStart < 0) {
                newStart = 0;
                newEnd = duration;
              }

              // 最终验证：确保startTime < endTime
              if (newStart >= newEnd) {
                console.warn(`平移字幕 ${id} 时检测到时间错乱，跳过更新`);
                return;
              }

              const startFormatted = store.formatCaptionTime(
                formatTime(newStart)
              );
              const endFormatted = store.formatCaptionTime(formatTime(newEnd));

              store.updateCaptionTimeFrame(id, startFormatted, endFormatted);
            }
          );
        }
      };

      const handleMouseUp = () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        setIsDragging(false);
        setIsSnappedEnd(false);
        isPanningMode = false;

        // 移除所有拖拽预览效果的类
        if (rightHandleEl) {
          rightHandleEl.classList.remove("handle-dragging");
          rightHandleEl.classList.remove("snapped");
        }

        const itemEl = document.getElementById(`caption-item-${caption.id}`);
        if (itemEl) {
          // 移除所有可能的类
          itemEl.classList.remove("resize-active");
          itemEl.classList.remove("panning-mode");
          itemEl.classList.remove("dragging-active");
        }

        // 拖拽结束后才进行排序和防止重叠处理
        store.captionManager.sortCaptionsByStartTime();

        // 使用新的智能碰撞检测和修复函数
        // 让CaptionManager处理碰撞检测和修复，确保数据同步
        store.captionManager.preventAllCaptionsOverlap();
        // 同步回store.captions
        store.captions = store.captionManager.captions;

        // 保存更改
        store.saveChange();
      };

      setIsDragging(true);
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      if (store.playing) {
        store.setPlaying(false);
      }
    },
    [
      caption.id,
      startTimeMs,
      endTimeMs,
      containerWidth,
      isDragging,
      store,
      allCaptions,
    ]
  );

  // 中间拖拽处理
  const handleCenterDrag = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (isDragging || !containerWidth) return;

      const initialX = event.clientX;
      const initialStartTime = startTimeMs;
      const duration = endTimeMs - startTimeMs;

      const itemEl = document.getElementById(`caption-item-${caption.id}`);
      const leftHandleEl = document.getElementById("left-handle");
      const rightHandleEl = document.getElementById("right-handle");
      const snapPoints = generateSnapPointsFromCaptions(
        allCaptions,
        caption.id
      );

      // 记录初始位置，用于计算拖拽距离
      const initialPosition = {
        x: event.clientX,
        y: event.clientY,
      };

      // 添加拖拽预览效果的状态
      let dragPreviewActive = false;
      let dragDistance = 0;

      const trackElement =
        event.currentTarget.closest(".timeline-container") ||
        document.querySelector(".timeline-container") ||
        event.currentTarget.parentElement?.parentElement;
      if (!trackElement) return;
      const trackRect = trackElement.getBoundingClientRect();

      const handleMouseMove = (moveEvent: MouseEvent) => {
        // 计算拖拽距离
        dragDistance = Math.sqrt(
          Math.pow(moveEvent.clientX - initialPosition.x, 2) +
            Math.pow(moveEvent.clientY - initialPosition.y, 2)
        );

        // 只有当拖拽距离超过阈值时才开始拖拽
        if (!dragPreviewActive && dragDistance > 5) {
          dragPreviewActive = true;
          // 添加拖拽预览效果的类
          if (itemEl) {
            itemEl.classList.add("dragging-active");
          }
        }

        const deltaX = moveEvent.clientX - initialX;
        const pixelsPerMs = trackRect.width / store.timelineDisplayDuration;
        const timeDelta = deltaX / pixelsPerMs;

        let newStartTime = Math.max(0, initialStartTime + timeDelta);
        let newEndTime = newStartTime + duration;

        // 生成吸附点
        // const snapPoints = generateSnapPointsFromCaptions(
        //   allCaptions,
        //   caption.id
        // );

        // 检查开始时间是否应该吸附
        const startSnapResult = findNearestSnapPoint(newStartTime, snapPoints);
        if (startSnapResult.snapped) {
          newStartTime = startSnapResult.time;
          newEndTime = newStartTime + duration;
          setIsSnappedStart(true);
        } else {
          setIsSnappedStart(false);
        }

        // 检查结束时间是否应该吸附
        const endSnapResult = findNearestSnapPoint(newEndTime, snapPoints);
        if (endSnapResult.snapped) {
          newEndTime = endSnapResult.time;
          newStartTime = newEndTime - duration;
          // 确保吸附后startTime不会大于endTime或小于0
          if (newStartTime < 0) {
            newStartTime = 0;
            newEndTime = duration;
          }
          setIsSnappedEnd(true);
        } else {
          setIsSnappedEnd(false);
        }

        // 检查是否与其他字幕重叠
        const result = checkTimeOverlap(
          newStartTime,
          newEndTime,
          allCaptions,
          caption.id
        );

        // 更新碰撞视觉反馈
        // const itemEl = document.getElementById(`caption-item-${caption.id}`);
        handleCollisionFeedback(itemEl, result.hasOverlap, "center");

        if (result.hasOverlap) {
          // 保持当前字幕的duration不变
          newStartTime = result.startTime;
          newEndTime = result.endTime;

          // 查找前后相邻的字幕并调整它们以避免重叠
          const precedingCaptions = allCaptions
            .filter((c) => c.id !== caption.id)
            .filter((c) => timeStringToMs(c.endTime) <= newStartTime)
            .sort(
              (a, b) => timeStringToMs(b.endTime) - timeStringToMs(a.endTime)
            );

          const followingCaptions = allCaptions
            .filter((c) => c.id !== caption.id)
            .filter((c) => timeStringToMs(c.startTime) >= newEndTime)
            .sort(
              (a, b) =>
                timeStringToMs(a.startTime) - timeStringToMs(b.startTime)
            );

          // 调整前一个字幕的endTime，如果太近或重叠
          if (precedingCaptions.length > 0) {
            const prevCaption = precedingCaptions[0];
            const prevEndTimeMs = timeStringToMs(prevCaption.endTime);

            if (
              prevEndTimeMs > newStartTime ||
              Math.abs(prevEndTimeMs - newStartTime) < 1
            ) {
              const adjustedEndTime = Math.min(prevEndTimeMs, newStartTime);
              const endTimeFormatted = store.formatCaptionTime(
                formatTime(adjustedEndTime)
              );
              store.updateCaption(prevCaption.id, "endTime", endTimeFormatted);
            }
          }

          // 调整后一个字幕的startTime，如果太近或重叠
          if (followingCaptions.length > 0) {
            const nextCaption = followingCaptions[0];
            const nextStartTimeMs = timeStringToMs(nextCaption.startTime);

            if (
              nextStartTimeMs < newEndTime ||
              Math.abs(nextStartTimeMs - newEndTime) < 1
            ) {
              const adjustedStartTime = Math.max(nextStartTimeMs, newEndTime);
              const startTimeFormatted = store.formatCaptionTime(
                formatTime(adjustedStartTime)
              );
              store.updateCaption(
                nextCaption.id,
                "startTime",
                startTimeFormatted
              );
            }
          }

          // 重置吸附状态，因为碰撞检测可能改变了时间
          setIsSnappedStart(false);
          setIsSnappedEnd(false);
        }

        // 最终验证：确保startTime永远不会大于endTime
        if (newStartTime >= newEndTime) {
          console.warn("检测到startTime >= endTime，进行修复");
          // 保持duration不变，调整时间
          const safeDuration = Math.max(duration, 100); // 最小100ms
          if (newStartTime < 0) {
            newStartTime = 0;
            newEndTime = safeDuration;
          } else {
            // 保持endTime，调整startTime
            newStartTime = Math.max(0, newEndTime - safeDuration);
          }
        }

        // 二次验证：确保时间合理性
        if (newStartTime < 0) {
          newStartTime = 0;
        }
        if (newEndTime <= newStartTime) {
          newEndTime = newStartTime + Math.max(duration, 100);
        }

        const formattedStart = formatTime(newStartTime);
        const formattedEnd = formatTime(newEndTime);
        const startTimeFormatted = store.formatCaptionTime(formattedStart);
        const endTimeFormatted = store.formatCaptionTime(formattedEnd);

        store.updateCaptionTimeFrame(
          caption.id,
          startTimeFormatted,
          endTimeFormatted
        );
      };

      const handleMouseUp = () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        setIsDragging(false);
        setIsSnappedStart(false);
        setIsSnappedEnd(false);

        // 移除所有拖拽预览效果的类
        if (leftHandleEl) {
          leftHandleEl.classList.remove("handle-dragging");
          leftHandleEl.classList.remove("snapped");
        }

        if (rightHandleEl) {
          rightHandleEl.classList.remove("handle-dragging");
          rightHandleEl.classList.remove("snapped");
        }

        const itemEl = document.getElementById(`caption-item-${caption.id}`);
        if (itemEl) {
          // 移除所有可能的类
          itemEl.classList.remove("dragging-active");
          itemEl.classList.remove("resize-active");
          itemEl.classList.remove("panning-mode");
        }

        // 拖拽结束后才进行排序和防止重叠处理
        store.captionManager.sortCaptionsByStartTime();

        // 使用新的智能碰撞检测和修复函数
        // 让CaptionManager处理碰撞检测和修复，确保数据同步
        store.captionManager.preventAllCaptionsOverlap();
        // 同步回store.captions
        store.captions = store.captionManager.captions;

        // 保存更改
        store.saveChange();
      };

      setIsDragging(true);
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      if (store.playing) {
        store.setPlaying(false);
      }

      event.stopPropagation();
      event.preventDefault();
    },
    [
      caption.id,
      startTimeMs,
      endTimeMs,
      containerWidth,
      isDragging,
      store,
      allCaptions,
    ]
  );

  return {
    handleLeftHandleDrag,
    handleRightHandleDrag,
    handleCenterDrag,
    isDragging,
    isSnappedStart,
    isSnappedEnd,
  };
};
