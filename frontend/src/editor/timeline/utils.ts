import { Caption, EditorElement } from "../../types";
import { timeStringToMs, formatTime } from "../../utils/timeUtils";

// 吸附阈值（毫秒）
export const SNAP_THRESHOLD = 300;

export const findFollowingCaptions = (
  fromCaptionId: string,
  fromEndTime: number,
  allCaptions: Caption[]
) => {
  const following: {
    id: string;
    startTime: number;
    endTime: number;
    offsetFromCurrent: number;
  }[] = [];

  const currentCaption = allCaptions.find((c) => c.id === fromCaptionId);
  if (!currentCaption) return following;

  const sortedCaptions = [...allCaptions].sort((a, b) => {
    return timeStringToMs(a.startTime) - timeStringToMs(b.startTime);
  });

  const currentIndex = sortedCaptions.findIndex((c) => c.id === fromCaptionId);
  if (currentIndex === -1 || currentIndex === sortedCaptions.length - 1) {
    return following;
  }

  const laterCaptions = sortedCaptions.slice(currentIndex + 1);

  laterCaptions.forEach((caption) => {
    const captionStartTime = timeStringToMs(caption.startTime);
    const captionEndTime = timeStringToMs(caption.endTime);
    const offsetFromCurrent = captionStartTime - fromEndTime;

    following.push({
      id: caption.id,
      startTime: captionStartTime,
      endTime: captionEndTime,
      offsetFromCurrent,
    });
  });

  return following;
};

/**
 * 查找后续元素
 * @param fromElementId 当前元素ID
 * @param fromEndTime 当前元素结束时间
 * @param allElements 所有元素
 * @returns 后续元素数组
 */
export const findFollowingElements = (
  fromElementId: string,
  fromEndTime: number,
  allElements: EditorElement[]
) => {
  const following: {
    id: string;
    startTime: number;
    endTime: number;
    offsetFromCurrent: number;
  }[] = [];

  const currentElement = allElements.find((e) => e.id === fromElementId);
  if (!currentElement) return following;

  const sortedElements = [...allElements].sort((a, b) => {
    return a.timeFrame.start - b.timeFrame.start;
  });

  const currentIndex = sortedElements.findIndex((e) => e.id === fromElementId);
  if (currentIndex === -1 || currentIndex === sortedElements.length - 1) {
    return following;
  }

  const laterElements = sortedElements.slice(currentIndex + 1);

  laterElements.forEach((element) => {
    const elementStartTime = element.timeFrame.start;
    const elementEndTime = element.timeFrame.end;
    const offsetFromCurrent = elementStartTime - fromEndTime;

    following.push({
      id: element.id,
      startTime: elementStartTime,
      endTime: elementEndTime,
      offsetFromCurrent,
    });
  });

  return following;
};

/**
 * 检查时间重叠并提供智能调整
 * @param checkStartTime 要检查的开始时间（毫秒）
 * @param checkEndTime 要检查的结束时间（毫秒）
 * @param items 所有项目数组（字幕或元素）
 * @param currentId 当前项目ID
 * @param trackId 当前轨道ID
 * @returns 调整后的时间和是否有重叠
 */
export const checkTimeOverlap = (
  checkStartTime: number,
  checkEndTime: number,
  items: Caption[] | EditorElement[],
  currentId: string,
  trackId?: string
) => {
  let adjustedStartTime = checkStartTime;
  let adjustedEndTime = checkEndTime;
  let hasOverlap = false;
  const duration = checkEndTime - checkStartTime;

  // 检查是否为EditorElement数组
  const isEditorElements = items.length > 0 && "timeFrame" in items[0];

  // 找出所有可能重叠的项目，并按照与当前项目的距离排序
  const overlappingItems = items
    .filter((item) => {
      if (item.id === currentId) return false;

      // 如果是EditorElement，检查是否在同一轨道上
      if (isEditorElements && trackId) {
        const element = item as EditorElement;
        if (element.trackId !== trackId) return false;
      }

      // 获取开始和结束时间
      const otherStart = isEditorElements
        ? (item as EditorElement).timeFrame.start
        : timeStringToMs((item as Caption).startTime);
      const otherEnd = isEditorElements
        ? (item as EditorElement).timeFrame.end
        : timeStringToMs((item as Caption).endTime);

      // 检查是否有重叠
      return checkStartTime < otherEnd && checkEndTime > otherStart;
    })
    .map((item) => {
      // 获取开始和结束时间
      const otherStart = isEditorElements
        ? (item as EditorElement).timeFrame.start
        : timeStringToMs((item as Caption).startTime);
      const otherEnd = isEditorElements
        ? (item as EditorElement).timeFrame.end
        : timeStringToMs((item as Caption).endTime);

      // 计算与当前项目的距离
      const distanceToStart = Math.abs(checkStartTime - otherStart);
      const distanceToEnd = Math.abs(checkEndTime - otherEnd);
      const minDistance = Math.min(
        Math.abs(checkStartTime - otherStart),
        Math.abs(checkStartTime - otherEnd),
        Math.abs(checkEndTime - otherStart),
        Math.abs(checkEndTime - otherEnd)
      );

      return {
        item,
        otherStart,
        otherEnd,
        distanceToStart,
        distanceToEnd,
        minDistance,
      };
    })
    .sort((a, b) => a.minDistance - b.minDistance);

  if (overlappingItems.length > 0) {
    hasOverlap = true;

    // 获取最近的重叠项目
    const nearest = overlappingItems[0];
    const { otherStart, otherEnd } = nearest;

    // 计算调整方向
    const distanceToEnd = Math.abs(checkStartTime - otherEnd);
    const distanceToStart = Math.abs(checkEndTime - otherStart);

    // 找出前一个和后一个项目（如果有）
    const sortedItems = [...items]
      .filter((item) => {
        if (item.id === currentId) return false;
        // 如果是EditorElement，检查是否在同一轨道上
        if (isEditorElements && trackId) {
          const element = item as EditorElement;
          return element.trackId === trackId;
        }
        return true;
      })
      .sort((a, b) => {
        const aStart = isEditorElements
          ? (a as EditorElement).timeFrame.start
          : timeStringToMs((a as Caption).startTime);
        const bStart = isEditorElements
          ? (b as EditorElement).timeFrame.start
          : timeStringToMs((b as Caption).startTime);
        return aStart - bStart;
      });

    const currentIndex = sortedItems.findIndex((item) => {
      const itemStart = isEditorElements
        ? (item as EditorElement).timeFrame.start
        : timeStringToMs((item as Caption).startTime);
      const itemEnd = isEditorElements
        ? (item as EditorElement).timeFrame.end
        : timeStringToMs((item as Caption).endTime);

      return (
        itemStart > checkStartTime ||
        (itemStart === checkStartTime && itemEnd > checkEndTime)
      );
    });

    const prevItem = currentIndex > 0 ? sortedItems[currentIndex - 1] : null;
    const nextItem =
      currentIndex >= 0 && currentIndex < sortedItems.length
        ? sortedItems[currentIndex]
        : null;

    const prevEndTime = prevItem
      ? isEditorElements
        ? (prevItem as EditorElement).timeFrame.end
        : timeStringToMs((prevItem as Caption).endTime)
      : 0;
    const nextStartTime = nextItem
      ? isEditorElements
        ? (nextItem as EditorElement).timeFrame.start
        : timeStringToMs((nextItem as Caption).startTime)
      : Infinity;

    // 计算可用空间
    const availableSpaceBefore = checkStartTime - prevEndTime;
    const availableSpaceAfter = nextStartTime - checkEndTime;
    const totalAvailableSpace = nextStartTime - prevEndTime;

    // 智能调整位置
    if (totalAvailableSpace >= duration) {
      // 有足够空间放置项目
      if (distanceToEnd < distanceToStart) {
        // 更靠近前一个项目的结束时间
        adjustedStartTime = otherEnd;
        adjustedEndTime = otherEnd + duration;

        // 确保不会与下一个项目重叠
        if (adjustedEndTime > nextStartTime) {
          adjustedEndTime = nextStartTime;
          adjustedStartTime = nextStartTime - duration;
        }
      } else {
        // 更靠近后一个项目的开始时间
        adjustedEndTime = otherStart;
        adjustedStartTime = otherStart - duration;

        // 确保不会与前一个项目重叠
        if (adjustedStartTime < prevEndTime) {
          adjustedStartTime = prevEndTime;
          adjustedEndTime = prevEndTime + duration;
        }
      }
    } else {
      // 空间不足，需要缩短项目持续时间
      const minDuration = 100; // 最小持续时间100毫秒

      if (availableSpaceBefore > availableSpaceAfter) {
        // 前面空间更大，放在前面
        adjustedStartTime = prevEndTime;
        adjustedEndTime = Math.min(prevEndTime + duration, nextStartTime);
      } else {
        // 后面空间更大，放在后面
        adjustedEndTime = nextStartTime;
        adjustedStartTime = Math.max(nextStartTime - duration, prevEndTime);
      }

      // 确保最小持续时间
      if (adjustedEndTime - adjustedStartTime < minDuration) {
        if (availableSpaceBefore > availableSpaceAfter) {
          adjustedEndTime = adjustedStartTime + minDuration;
        } else {
          adjustedStartTime = adjustedEndTime - minDuration;
        }
      }
    }
  }

  // 确保时间不为负
  adjustedStartTime = Math.max(0, adjustedStartTime);

  return {
    startTime: adjustedStartTime,
    endTime: adjustedEndTime,
    hasOverlap,
  };
};

/**
 * 检查时间是否应该吸附到目标时间点
 * @param time 当前时间（毫秒）
 * @param targetTime 目标时间（毫秒）
 * @param threshold 吸附阈值（毫秒）
 * @returns 如果应该吸附，返回目标时间；否则返回原始时间
 */
export const snapToTime = (
  time: number,
  targetTime: number,
  threshold: number = SNAP_THRESHOLD
): { snapped: boolean; time: number } => {
  const diff = Math.abs(time - targetTime);
  if (diff <= threshold) {
    return { snapped: true, time: targetTime };
  }
  return { snapped: false, time };
};

/**
 * 查找最近的吸附点
 * @param time 当前时间（毫秒）
 * @param snapPoints 可能的吸附点数组（毫秒）
 * @param threshold 吸附阈值（毫秒）
 * @returns 吸附结果
 */
export const findNearestSnapPoint = (
  time: number,
  snapPoints: number[],
  threshold: number = SNAP_THRESHOLD
): { snapped: boolean; time: number } => {
  let nearestPoint = time;
  let minDiff = threshold + 1; // 初始化为大于阈值的值
  let snapped = false;

  snapPoints.forEach((point) => {
    const diff = Math.abs(time - point);
    if (diff <= threshold && diff < minDiff) {
      minDiff = diff;
      nearestPoint = point;
      snapped = true;
    }
  });

  return { snapped, time: nearestPoint };
};

/**
 * 从字幕列表中生成吸附点
 * @param captions 字幕数组
 * @param excludeId 要排除的字幕ID
 * @returns 吸附点数组（毫秒）
 */
export const generateSnapPointsFromCaptions = (
  captions: Caption[],
  excludeId?: string
): number[] => {
  const snapPoints: number[] = [];

  captions.forEach((caption) => {
    if (caption.id !== excludeId) {
      const startTime = timeStringToMs(caption.startTime);
      const endTime = timeStringToMs(caption.endTime);

      // 添加开始和结束时间作为吸附点
      snapPoints.push(startTime);
      snapPoints.push(endTime);
    }
  });

  // 添加0作为吸附点
  snapPoints.push(0);

  return [...new Set(snapPoints)]; // 去重
};

/**
 * 从元素列表中生成吸附点
 * @param elements 元素数组
 * @param excludeId 要排除的元素ID
 * @returns 吸附点数组（毫秒）
 */
export const generateSnapPointsFromElements = (
  elements: EditorElement[],
  excludeId?: string
): number[] => {
  const snapPoints: number[] = [];

  elements.forEach((element) => {
    if (element.id !== excludeId) {
      // 添加开始和结束时间作为吸附点
      snapPoints.push(element.timeFrame.start);
      snapPoints.push(element.timeFrame.end);
    }
  });

  // 添加0作为吸附点
  snapPoints.push(0);

  return [...new Set(snapPoints)]; // 去重
};

/**
 * 检查并修复所有字幕之间的碰撞
 * @param captions 字幕数组
 * @returns 修复后的字幕数组
 */
export const fixAllCaptionCollisions = (captions: Caption[]): Caption[] => {
  if (captions.length <= 1) return captions;

  // 首先按开始时间排序
  const sortedCaptions = [...captions].sort(
    (a, b) => timeStringToMs(a.startTime) - timeStringToMs(b.startTime)
  );

  // 逐个检查并修复碰撞
  for (let i = 1; i < sortedCaptions.length; i++) {
    const currentCaption = sortedCaptions[i];
    const previousCaption = sortedCaptions[i - 1];

    const currentStartTime = timeStringToMs(currentCaption.startTime);
    const currentEndTime = timeStringToMs(currentCaption.endTime);
    const previousEndTime = timeStringToMs(previousCaption.endTime);

    // 检查是否有碰撞
    if (currentStartTime < previousEndTime) {
      // 计算当前字幕的持续时间
      const currentDuration = currentEndTime - currentStartTime;
      const minDuration = 1000; // 最小持续时间1秒

      // 调整当前字幕的开始时间
      const newStartTime = previousEndTime;

      // 检查是否有下一个字幕
      const nextCaption =
        i < sortedCaptions.length - 1 ? sortedCaptions[i + 1] : null;
      const nextStartTime = nextCaption
        ? timeStringToMs(nextCaption.startTime)
        : Infinity;

      // 计算新的结束时间，保持原有持续时间，但不超过下一个字幕的开始时间
      let newEndTime = newStartTime + currentDuration;

      // 如果新的结束时间超过了下一个字幕的开始时间，需要调整
      if (newEndTime > nextStartTime) {
        // 如果两个字幕之间的空间不足以容纳当前字幕的持续时间
        if (nextStartTime - newStartTime < currentDuration) {
          // 使用可用空间
          newEndTime = nextStartTime;

          // 如果可用空间小于最小持续时间，强制设置最小持续时间
          if (newEndTime - newStartTime < minDuration) {
            newEndTime = newStartTime + minDuration;

            // 如果这会导致与下一个字幕重叠，需要递归处理后续字幕
            if (newEndTime > nextStartTime && nextCaption) {
              // 更新当前字幕
              sortedCaptions[i] = {
                ...currentCaption,
                startTime: formatTime(newStartTime),
                endTime: formatTime(newEndTime),
              };

              // 递归处理后续字幕
              const remainingCaptions = fixAllCaptionCollisions(
                sortedCaptions.slice(i)
              );
              sortedCaptions.splice(
                i,
                sortedCaptions.length - i,
                ...remainingCaptions
              );

              // 跳过已处理的字幕
              continue;
            }
          }
        }
      }

      // 更新当前字幕
      sortedCaptions[i] = {
        ...currentCaption,
        startTime: formatTime(newStartTime),
        endTime: formatTime(newEndTime),
      };
    }
  }

  return sortedCaptions;
};
